#!/usr/bin/env python3
"""
Test script to verify all bug fixes are working correctly
"""

import sys
import os

def test_imports():
    """Test that all imports work correctly"""
    print("🧪 Testing imports...")
    try:
        from app import (
            validate_url, 
            extract_url_features, 
            load_model, 
            predict_phishing,
            analyze_url_with_ai
        )
        print("✅ All imports successful")
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_url_validation():
    """Test URL validation function"""
    print("\n🔍 Testing URL validation...")
    
    test_cases = [
        ("https://google.com", True),
        ("http://example.com", True),
        ("github.com", True),
        ("invalid-url", False),
        ("malformed..url", False),
        ("", False),
        ("   ", False),
        ("http://", False),
        ("https://", False),
        ("ftp://example.com", False),  # FTP URLs should be rejected
        ("http://sub.domain.com", True),
        ("https://very-long-domain-name.co.uk", True)
    ]
    
    from app import validate_url
    
    passed = 0
    total = len(test_cases)
    
    for url, expected in test_cases:
        result = validate_url(url)
        status = "✅" if result == expected else "❌"
        print(f"  {url:<35} -> {result:<5} (expected {expected}) {status}")
        if result == expected:
            passed += 1
    
    print(f"\n📊 URL Validation: {passed}/{total} tests passed")
    return passed == total

def test_feature_extraction():
    """Test feature extraction function"""
    print("\n📊 Testing feature extraction...")
    
    from app import extract_url_features
    
    test_cases = [
        "https://google.com",
        "http://example.com",
        "https://sub.domain.co.uk",
        "http://test-site.com",
        "https://<EMAIL>"
    ]
    
    passed = 0
    for url in test_cases:
        try:
            features = extract_url_features(url)
            if len(features) == 5 and all(isinstance(f, (int, float)) for f in features):
                print(f"  ✅ {url:<30} -> {features}")
                passed += 1
            else:
                print(f"  ❌ {url:<30} -> Invalid features: {features}")
        except Exception as e:
            print(f"  ❌ {url:<30} -> Error: {e}")
    
    print(f"\n📊 Feature Extraction: {passed}/{len(test_cases)} tests passed")
    return passed == len(test_cases)

def test_model_loading():
    """Test model loading"""
    print("\n🤖 Testing model loading...")
    
    from app import load_model
    
    try:
        result = load_model()
        if result:
            print("✅ Model loaded successfully")
            return True
        else:
            print("❌ Model loading failed")
            return False
    except Exception as e:
        print(f"❌ Model loading error: {e}")
        return False

def test_prediction():
    """Test prediction functionality"""
    print("\n🔮 Testing prediction functionality...")
    
    from app import predict_phishing, load_model
    
    # Ensure model is loaded
    if not load_model():
        print("❌ Cannot test prediction - model not loaded")
        return False
    
    test_urls = [
        "https://google.com",
        "http://example.com",
        "https://github.com"
    ]
    
    passed = 0
    for url in test_urls:
        try:
            prediction, confidence_class, ai_analysis = predict_phishing(url, use_ai=False)
            if prediction and confidence_class:
                print(f"  ✅ {url:<25} -> {prediction} ({confidence_class})")
                passed += 1
            else:
                print(f"  ❌ {url:<25} -> Invalid prediction result")
        except Exception as e:
            print(f"  ❌ {url:<25} -> Error: {e}")
    
    print(f"\n📊 Prediction: {passed}/{len(test_urls)} tests passed")
    return passed == len(test_urls)

def test_ai_analysis():
    """Test AI analysis (should handle gracefully when not available)"""
    print("\n🤖 Testing AI analysis (graceful degradation)...")
    
    from app import analyze_url_with_ai
    
    try:
        result = analyze_url_with_ai("https://example.com")
        
        # Should return a valid structure even when AI is not available
        required_keys = ['is_phishing', 'confidence', 'reasons', 'recommendation']
        if all(key in result for key in required_keys):
            print("✅ AI analysis returns valid structure (graceful degradation)")
            return True
        else:
            print(f"❌ AI analysis missing required keys: {result}")
            return False
    except Exception as e:
        print(f"❌ AI analysis error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Running comprehensive bug fix tests...\n")
    
    tests = [
        ("Imports", test_imports),
        ("URL Validation", test_url_validation),
        ("Feature Extraction", test_feature_extraction),
        ("Model Loading", test_model_loading),
        ("Prediction", test_prediction),
        ("AI Analysis", test_ai_analysis)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} test PASSED\n")
            else:
                print(f"❌ {test_name} test FAILED\n")
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {e}\n")
    
    print("=" * 50)
    print(f"📊 FINAL RESULTS: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! The application is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
