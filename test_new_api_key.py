#!/usr/bin/env python3
"""
Test script to verify the new OpenAI API key is working
"""

print('🧪 Testing complete application with new API key and updated model...')

try:
    from app import predict_phishing, analyze_url_with_ai, validate_url, extract_url_features
    
    # Test URLs
    test_urls = [
        'https://google.com',
        'https://suspicious-phishing-site.com',
        'http://secure-bank-login.fake-domain.com'
    ]
    
    for test_url in test_urls:
        print(f'\n🔍 Testing URL: {test_url}')
        
        # Test URL validation
        if validate_url(test_url):
            print('✅ URL validation passed')
        else:
            print('❌ URL validation failed')
            continue
        
        # Test feature extraction
        features = extract_url_features(test_url)
        print(f'📊 Features: {features}')
        
        # Test ML prediction
        prediction, confidence_class, ai_analysis = predict_phishing(test_url, use_ai=True)
        print(f'🔮 ML Prediction: {prediction}')
        print(f'🎯 Confidence: {confidence_class}')
        print(f'🤖 AI Analysis: {ai_analysis.get("recommendation", "N/A")}')
    
    print('\n🎉 Application working perfectly with new API key!')
    print('ℹ️ Note: AI features will work once you add credits to your OpenAI account')
    
except Exception as e:
    print(f'❌ Error: {e}')
    import traceback
    traceback.print_exc()
